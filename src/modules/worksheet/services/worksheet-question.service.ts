import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  ConflictException,
  Logger
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectModel } from '@nestjs/mongoose';
import { Repository } from 'typeorm';
import { Model } from 'mongoose';

import { v4 as uuidv4 } from 'uuid';

import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { WorksheetPromptResult } from '../../mongodb/schemas/worksheet-prompt-result.schema';
import { CreateExerciseQuestionDto } from '../dto/exercise-question.dto';
import { AddQuestionToWorksheetDto, UpdateWorksheetQuestionDto, ReplaceWorksheetQuestionDto, BulkReorderQuestionsDto, ReorderQuestionDto } from '../dto/worksheet-question.dto';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';
import { EUserRole } from '../../user/dto/create-user.dto';
import { WorksheetQuestionAuditService } from './worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { QuestionPoolService } from '../../question-pool/question-pool.service';
import { QuestionPool } from '../../mongodb/schemas/question-pool.schema';
import { UserQuestionHistoryService } from '../../question-pool/services/user-question-history.service';
import { WorksheetQuestionCollaborationGateway } from '../gateways/worksheet-question-collaboration.gateway';
import { WorksheetQuestionLockingService } from './worksheet-question-locking.service';
import { CollaborationEvent } from '../enums/collaboration-events.enum';
import { MonitorDbPerformance, MonitorConnectionPool } from '../decorators/db-performance.decorator';
import { WorksheetQuestionMetricsService } from './worksheet-question-metrics.service';
import { WorksheetQuestionEnhancedCacheService } from './worksheet-question-enhanced-cache.service';
import { ExerciseQuestionItem } from '../../prompt/interfaces/exercise-result.interface';

export interface UserContext {
  sub: string;
  email: string;
  role: EUserRole;
  schoolId?: string | null;
}

@Injectable()
export class WorksheetQuestionService {
  private readonly logger = new Logger(WorksheetQuestionService.name);

  constructor(
    @InjectRepository(Worksheet)
    private readonly worksheetRepository: Repository<Worksheet>,
    @InjectModel(WorksheetQuestionDocument.name)
    private readonly worksheetQuestionModel: Model<WorksheetQuestionDocument>,
    @InjectModel(WorksheetPromptResult.name)
    private readonly worksheetPromptResultModel: Model<WorksheetPromptResult>,
    private readonly auditService: WorksheetQuestionAuditService,
    private readonly socketGateway: SocketGateway,
    private readonly collaborationGateway: WorksheetQuestionCollaborationGateway,
    private readonly lockingService: WorksheetQuestionLockingService,
    private readonly worksheetQuestionMetricsService: WorksheetQuestionMetricsService,
    private readonly enhancedCacheService: WorksheetQuestionEnhancedCacheService,
    private readonly questionPoolService: QuestionPoolService,
    private readonly userQuestionHistoryService: UserQuestionHistoryService,
  ) {}

  // ============================================================================
  // HELPER METHODS FOR WORKSHEETPROMPTRESULT OPERATIONS
  // ============================================================================

  /**
   * Find a question by ID in the WorksheetPromptResult document
   */
  private async findQuestionInPromptResult(
    worksheetId: string,
    questionId: string
  ): Promise<{ promptResult: WorksheetPromptResult; question: ExerciseQuestionItem; index: number } | null> {
    this.logger.debug(`Searching for question ${questionId} in worksheet ${worksheetId}`);

    const promptResult = await this.worksheetPromptResultModel.findOne({
      worksheetId: worksheetId
    });

    if (!promptResult) {
      this.logger.debug(`WorksheetPromptResult document not found for worksheet ${worksheetId}`);
      return null;
    }

    if (!promptResult.promptResult?.result) {
      this.logger.debug(`No questions array found in WorksheetPromptResult for worksheet ${worksheetId}`);
      return null;
    }

    // Log available question IDs for debugging
    const availableIds = promptResult.promptResult.result.map((q: any) => q.id).filter(Boolean);
    this.logger.debug(`Available question IDs: [${availableIds.join(', ')}], searching for: ${questionId}`);

    const questionIndex = promptResult.promptResult.result.findIndex(
      (q: any) => q.id === questionId
    );

    if (questionIndex === -1) {
      this.logger.debug(`Question ${questionId} not found in available IDs: [${availableIds.join(', ')}]`);
      return null;
    }

    this.logger.debug(`Found question ${questionId} at index ${questionIndex}`);
    return {
      promptResult,
      question: promptResult.promptResult.result[questionIndex],
      index: questionIndex
    };
  }

  /**
   * Add a question to the WorksheetPromptResult document
   */
  private async addQuestionToPromptResult(
    worksheetId: string,
    questionData: ExerciseQuestionItem,
    position?: number
  ): Promise<void> {
    const promptResult = await this.worksheetPromptResultModel.findOne({
      worksheetId: worksheetId
    });

    if (!promptResult) {
      throw new NotFoundException(`WorksheetPromptResult not found for worksheet ${worksheetId}`);
    }

    if (!promptResult.promptResult) {
      promptResult.promptResult = { result: [] };
    }

    if (!promptResult.promptResult.result) {
      promptResult.promptResult.result = [];
    }

    // Add question at specified position or at the end
    if (position !== undefined && position >= 0 && position <= promptResult.promptResult.result.length) {
      promptResult.promptResult.result.splice(position, 0, questionData);
    } else {
      promptResult.promptResult.result.push(questionData);
    }

    // Update counts
    promptResult.currentQuestionCount = promptResult.promptResult.result.length;
    promptResult.totalQuestionCount = promptResult.promptResult.result.length;
    promptResult.updatedAt = new Date();

    this.logger.log(`📝 Saving question to MongoDB. New count: ${promptResult.promptResult.result.length}`);
    await promptResult.save();
    this.logger.log(`✅ Successfully saved question to MongoDB for worksheet ${worksheetId}`);
  }

  /**
   * Update a question in the WorksheetPromptResult document
   */
  private async updateQuestionInPromptResult(
    worksheetId: string,
    questionId: string,
    updates: Partial<ExerciseQuestionItem>
  ): Promise<ExerciseQuestionItem | null> {
    const result = await this.findQuestionInPromptResult(worksheetId, questionId);
    if (!result) {
      return null;
    }

    const { promptResult, index } = result;

    // Apply updates to the question
    Object.assign(promptResult.promptResult.result[index], updates);

    // Update timestamp
    promptResult.updatedAt = new Date();

    await promptResult.save();

    return promptResult.promptResult.result[index];
  }

  /**
   * Remove a question from the WorksheetPromptResult document
   */
  private async removeQuestionFromPromptResult(
    worksheetId: string,
    questionId: string
  ): Promise<ExerciseQuestionItem | null> {
    this.logger.debug(`Attempting to remove question ${questionId} from MongoDB for worksheet ${worksheetId}`);

    // First, check if WorksheetPromptResult document exists
    const promptResult = await this.worksheetPromptResultModel.findOne({
      worksheetId: worksheetId
    });

    if (!promptResult) {
      this.logger.error(`WorksheetPromptResult document not found for worksheet ${worksheetId}`);
      throw new NotFoundException(`WorksheetPromptResult not found for worksheet ${worksheetId}`);
    }

    if (!promptResult.promptResult?.result) {
      this.logger.error(`No questions array found in WorksheetPromptResult for worksheet ${worksheetId}`);
      throw new NotFoundException(`No questions found in WorksheetPromptResult for worksheet ${worksheetId}`);
    }

    // Log all question IDs for debugging
    const existingQuestionIds = promptResult.promptResult.result.map((q: any) => q.id).filter(Boolean);
    this.logger.debug(`Existing question IDs in MongoDB: [${existingQuestionIds.join(', ')}]`);
    this.logger.debug(`Looking for question ID: ${questionId} (type: ${typeof questionId})`);

    const questionIndex = promptResult.promptResult.result.findIndex(
      (q: any) => q.id === questionId
    );

    if (questionIndex === -1) {
      this.logger.error(`Question ${questionId} not found in MongoDB. Available IDs: [${existingQuestionIds.join(', ')}]`);
      throw new NotFoundException(`Question ${questionId} not found in WorksheetPromptResult for worksheet ${worksheetId}`);
    }

    const question = promptResult.promptResult.result[questionIndex];
    this.logger.debug(`Found question at index ${questionIndex}, removing from MongoDB`);

    // Remove the question from the array
    promptResult.promptResult.result.splice(questionIndex, 1);

    // Update counts
    promptResult.currentQuestionCount = promptResult.promptResult.result.length;
    promptResult.totalQuestionCount = promptResult.promptResult.result.length;
    promptResult.updatedAt = new Date();

    try {
      await promptResult.save();
      this.logger.log(`Successfully removed question ${questionId} from MongoDB for worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Failed to save WorksheetPromptResult after removing question ${questionId}:`, error);
      throw error;
    }

    return question;
  }

  /**
   * Bulk remove multiple questions from the WorksheetPromptResult document
   */
  private async bulkRemoveQuestionsFromPromptResult(
    worksheetId: string,
    questionIds: string[]
  ): Promise<string[]> {
    this.logger.debug(`Bulk removing ${questionIds.length} questions from MongoDB for worksheet ${worksheetId}`);

    // First, check if WorksheetPromptResult document exists
    const promptResult = await this.worksheetPromptResultModel.findOne({
      worksheetId: worksheetId
    });

    if (!promptResult) {
      this.logger.error(`WorksheetPromptResult document not found for worksheet ${worksheetId}`);
      throw new NotFoundException(`WorksheetPromptResult not found for worksheet ${worksheetId}`);
    }

    if (!promptResult.promptResult?.result) {
      this.logger.error(`No questions array found in WorksheetPromptResult for worksheet ${worksheetId}`);
      throw new NotFoundException(`No questions found in WorksheetPromptResult for worksheet ${worksheetId}`);
    }

    const originalCount = promptResult.promptResult.result.length;
    const existingQuestionIds = promptResult.promptResult.result.map((q: any) => q.id).filter(Boolean);

    this.logger.debug(`Original question count: ${originalCount}`);
    this.logger.debug(`Existing question IDs: [${existingQuestionIds.join(', ')}]`);
    this.logger.debug(`Questions to remove: [${questionIds.join(', ')}]`);

    // Filter out questions that should be removed
    const remainingQuestions = promptResult.promptResult.result.filter(
      (q: any) => !questionIds.includes(q.id)
    );

    const removedCount = originalCount - remainingQuestions.length;
    const actuallyRemoved = questionIds.filter(id => existingQuestionIds.includes(id));

    this.logger.debug(`Questions actually removed: ${removedCount} (${actuallyRemoved.join(', ')})`);

    // Update the questions array
    promptResult.promptResult.result = remainingQuestions;

    // Update counts - THIS IS THE KEY FIX
    promptResult.currentQuestionCount = remainingQuestions.length;
    promptResult.totalQuestionCount = remainingQuestions.length;
    promptResult.updatedAt = new Date();

    try {
      await promptResult.save();
      this.logger.log(`Successfully bulk removed ${removedCount} questions from MongoDB for worksheet ${worksheetId}`);
      this.logger.log(`Updated currentQuestionCount and totalQuestionCount to ${remainingQuestions.length}`);
    } catch (error) {
      this.logger.error(`Failed to save WorksheetPromptResult after bulk removing questions:`, error);
      throw error;
    }

    return actuallyRemoved;
  }

  /**
   * Utility method to ensure MongoDB question counts are always consistent
   * Call this after any direct MongoDB operations that modify the questions array
   */
  private async ensureMongoDBCountsConsistent(worksheetId: string): Promise<void> {
    try {
      const promptResult = await this.worksheetPromptResultModel.findOne({
        worksheetId: worksheetId
      });

      if (!promptResult || !promptResult.promptResult?.result) {
        this.logger.warn(`Cannot ensure counts consistency - WorksheetPromptResult not found for worksheet ${worksheetId}`);
        return;
      }

      const actualQuestionCount = promptResult.promptResult.result.length;
      const currentCount = promptResult.currentQuestionCount;
      const totalCount = promptResult.totalQuestionCount;

      // Check if counts are inconsistent
      if (currentCount !== actualQuestionCount || totalCount !== actualQuestionCount) {
        this.logger.warn(`MongoDB count inconsistency detected for worksheet ${worksheetId}: actual=${actualQuestionCount}, current=${currentCount}, total=${totalCount}`);

        // Fix the counts
        await this.worksheetPromptResultModel.updateOne(
          { worksheetId: worksheetId },
          {
            $set: {
              currentQuestionCount: actualQuestionCount,
              totalQuestionCount: actualQuestionCount,
              updatedAt: new Date()
            }
          }
        );

        this.logger.log(`Fixed MongoDB count inconsistency for worksheet ${worksheetId}: updated to ${actualQuestionCount}`);
      } else {
        this.logger.debug(`MongoDB counts are consistent for worksheet ${worksheetId}: ${actualQuestionCount} questions`);
      }
    } catch (error) {
      this.logger.error(`Error ensuring MongoDB counts consistency for worksheet ${worksheetId}:`, error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Convert ExerciseQuestionItem to IExerciseQuestion interface
   */
  private convertExerciseQuestionItemToIExerciseQuestion(
    item: ExerciseQuestionItem,
    position?: number
  ): IExerciseQuestion {
    return {
      id: item.id || item.questionId, // Use id field from promptResult.result
      position: position || 1,
      order: position || 1, // For backward compatibility
      points: item.points || 1,
      type: item.type as any,
      content: item.content,
      options: item.options || [],
      answer: item.answer || [],
      explain: item.explain || '',
      subject: item.subject,
      parentSubject: item.parentSubject,
      childSubject: item.childSubject,
      topic: item.topic,
      subtopic: item.subtopic,
      grade: item.grade,
      difficulty: item.difficulty as any,
      media: item.media,
      imagePrompt: item.imagePrompt,
      imageUrl: item.imageUrl,
      image: item.image,
      status: item.status as any,
      metadata: item.metadata,
      audit: item.audit,
      schoolId: item.schoolId
    };
  }

  /**
   * Convert IExerciseQuestion to ExerciseQuestionItem
   */
  private convertIExerciseQuestionToExerciseQuestionItem(
    question: IExerciseQuestion
  ): ExerciseQuestionItem {
    return {
      id: question.id,
      type: question.type,
      content: question.content,
      options: question.options,
      answer: question.answer,
      explain: question.explain,
      imagePrompt: question.imagePrompt,
      subject: question.subject,
      parentSubject: question.parentSubject,
      childSubject: question.childSubject,
      topic: question.topic,
      subtopic: question.subtopic,
      grade: question.grade,
      difficulty: question.difficulty,
      media: question.media,
      imageUrl: question.imageUrl,
      image: question.image,
      status: question.status,
      metadata: question.metadata,
      audit: question.audit,
      schoolId: question.schoolId,
      points: question.points
    };
  }

  // ============================================================================
  // MAIN SERVICE METHODS
  // ============================================================================

  /**
   * Add a new question to a worksheet
   */
  async addQuestionToWorksheet(
    worksheetId: string,
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    this.logger.log(`Adding question to worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 2: Check question limit
    await this.validateQuestionLimit(worksheet);

    // Step 3: Generate a UUID for the question
    const questionId = uuidv4();

    // Step 4: Create the question in WorksheetPromptResult
    const newQuestion = await this.createQuestionInMongoDB(questionDto, user, worksheet, questionId);

    // Step 5: Update worksheet questionIds array in PostgreSQL
    await this.addQuestionIdToWorksheet(worksheet, questionId, user);

    // Step 5: Update cache with fresh data from WorksheetPromptResult
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);

    // Step 6: Emit real-time updates (both legacy and collaboration)
    const questionForResponse = newQuestion;
    await this.emitQuestionUpdate(worksheetId, 'question_added', {
      question: questionForResponse,
      totalQuestions: worksheet.questionIds?.length || 0,
      worksheetId
    }, user.sub);

    // Step 6b: Emit collaboration event
    await this.collaborationGateway.broadcastQuestionUpdate(
      worksheetId,
      CollaborationEvent.QUESTION_ADDED_REALTIME,
      {
        question: questionForResponse,
        totalQuestions: worksheet.questionIds?.length || 0,
        addedBy: user.sub
      },
      user.sub
    );

    // Step 7: Log audit event
    await this.auditService.logQuestionAdded(worksheetId, questionId, user);

    this.logger.log(`Successfully added question ${questionId} to worksheet ${worksheetId}`);
    return questionForResponse;
  }

  /**
   * Validate user access to worksheet and return worksheet
   */
  @MonitorDbPerformance('findWorksheet', 'worksheets')
  @MonitorConnectionPool()
  private async validateWorksheetAccess(
    worksheetId: string,
    user: UserContext
  ): Promise<Worksheet> {
    this.logger.debug('User:', user);
    const worksheet = await this.worksheetRepository.findOne({
      where: { id: worksheetId },
      relations: ['selectedOptions']
    });

    if (!worksheet) {
      throw new NotFoundException(`Worksheet with ID ${worksheetId} not found`);
    }

    // Admin has access to all worksheets
    if (user.role === EUserRole.ADMIN) {
      return worksheet;
    }

    // School-based access control
    if (user.role === EUserRole.SCHOOL_MANAGER) {
      if (!user.schoolId) {
        throw new ForbiddenException('School manager must be assigned to a school');
      }
      if (worksheet.schoolId !== user.schoolId) {
        throw new ForbiddenException('Cannot modify questions from different school');
      }
      return worksheet;
    }

    // Independent teacher can only modify their own worksheets
    if (user.role === EUserRole.INDEPENDENT_TEACHER) {
      if (!user.schoolId || worksheet.schoolId !== user.schoolId) {
        throw new ForbiddenException('Can only modify your own worksheets');
      }
      return worksheet;
    }

    // Regular teacher can modify worksheets in their school
    if (user.role === EUserRole.TEACHER) {
      if (!user.schoolId || worksheet.schoolId !== user.schoolId) {
        throw new ForbiddenException('Access denied to worksheet from different school');
      }
      return worksheet;
    }

    throw new ForbiddenException('Insufficient permissions to modify worksheet questions');
  }

  /**
   * Validate that adding a question won't exceed the limit
   */
  private async validateQuestionLimit(worksheet: Worksheet): Promise<void> {
    const currentQuestionCount = worksheet.questionIds?.length || 0;
    const maxQuestions = worksheet.maxQuestions || 100;

    if (currentQuestionCount >= maxQuestions) {
      throw new BadRequestException(
        `Question limit exceeded. Current: ${currentQuestionCount}, Maximum: ${maxQuestions}`
      );
    }
  }

  /**
   * Create a new question in WorksheetPromptResult
   */
  private async createQuestionInMongoDB(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet,
    questionId: string
  ): Promise<IExerciseQuestion> {
    const currentQuestionCount = worksheet.questionIds?.length || 0;
    const position = questionDto.position || currentQuestionCount + 1;

    // If questionPoolId is provided, get the question from the pool
    if (questionDto.questionPoolId) {
      return this.createQuestionFromPool(questionDto, user, worksheet, questionId, position);
    }

    // Otherwise, create a new question from the provided data
    const newQuestion = this.createQuestionFromDto(questionDto, user, worksheet, questionId, position);

    // Convert to ExerciseQuestionItem and add to WorksheetPromptResult
    const questionItem = this.convertIExerciseQuestionToExerciseQuestionItem(newQuestion);
    await this.addQuestionToPromptResult(worksheet.id, questionItem, position - 1);

    return newQuestion;
  }

  /**
   * Create a question document from the question pool
   */
  private async createQuestionDocFromPool(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet,
    position: number,
    questionId: string
  ): Promise<WorksheetQuestionDocument> {
    // Get the question from the pool
    const poolQuestion = await this.questionPoolService.getQuestionById(questionDto.questionPoolId!);

    if (!poolQuestion) {
      throw new NotFoundException(`Question with ID ${questionDto.questionPoolId} not found in question pool`);
    }

    // Validate user access to the pool question
    this.validatePoolQuestionAccess(poolQuestion, user);

    // Create the MongoDB document (let MongoDB generate the _id automatically)
    const questionDoc = new this.worksheetQuestionModel({
      worksheetId: worksheet.id,
      questionId, // Set the UUID questionId
      position,
      points: questionDto.points || 1,

      // Copy question content from pool
      type: poolQuestion.type,
      content: poolQuestion.content,
      options: poolQuestion.options || [],
      answer: poolQuestion.answer || [],
      explain: poolQuestion.explain || '',

      // Copy subject and academic information
      subject: poolQuestion.subject,
      parentSubject: poolQuestion.parentSubject,
      childSubject: poolQuestion.childSubject,
      grade: poolQuestion.grade,
      difficulty: poolQuestion.difficultyLevel,

      // Copy media information
      image: poolQuestion.image,
      imagePrompt: poolQuestion.imagePrompt,

      // Audit fields
      audit: {
        createdBy: user.sub,
        createdAt: new Date(),
        updatedBy: user.sub,
        updatedAt: new Date(),
        version: 1
      },

      // School association
      schoolId: user.schoolId || undefined,
      status: 'ACTIVE'
    });

    // Save to MongoDB
    const savedDoc = await questionDoc.save();

    // Update pool question usage statistics
    await this.updatePoolQuestionUsage(questionDto.questionPoolId!);

    return savedDoc;
  }

  /**
   * Create a question document from DTO data
   */
  private async createQuestionDocFromDto(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet,
    position: number,
    questionId: string
  ): Promise<WorksheetQuestionDocument> {
    // Create the MongoDB document (let MongoDB generate the _id automatically)
    const questionDoc = new this.worksheetQuestionModel({
      worksheetId: worksheet.id,
      questionId, // Set the UUID questionId
      position,
      points: questionDto.points || 1,
      type: questionDto.type,
      content: questionDto.content,
      options: questionDto.options,
      answer: questionDto.answer,
      explain: questionDto.explain,
      subject: questionDto.subject,
      parentSubject: questionDto.parentSubject,
      childSubject: questionDto.childSubject,
      topic: questionDto.topic,
      subtopic: questionDto.subtopic,
      grade: questionDto.grade,
      difficulty: questionDto.difficulty,
      media: questionDto.media,
      imagePrompt: questionDto.imagePrompt,
      imageUrl: questionDto.imageUrl,
      status: questionDto.status || 'ACTIVE',
      metadata: questionDto.metadata,
      // Audit fields
      audit: {
        createdBy: user.sub,
        createdAt: new Date(),
        updatedBy: user.sub,
        updatedAt: new Date(),
        version: 1
      },
      // School association
      schoolId: user.schoolId || undefined
    });

    // Save to MongoDB
    return await questionDoc.save();
  }

  /**
   * Add question ID to worksheet's questionIds array in PostgreSQL
   */
  private async addQuestionIdToWorksheet(
    worksheet: Worksheet,
    questionId: string,
    user: UserContext
  ): Promise<void> {
    this.logger.log(`📝 Adding question ID ${questionId} to PostgreSQL worksheet ${worksheet.id}`);

    if (!worksheet.questionIds) {
      worksheet.questionIds = [];
    }

    const beforeCount = worksheet.questionIds.length;
    worksheet.questionIds.push(questionId);
    worksheet.totalQuestions = worksheet.questionIds.length;
    worksheet.lastModifiedBy = user.sub;

    await this.worksheetRepository.save(worksheet);

    this.logger.log(`✅ PostgreSQL updated: ${beforeCount} -> ${worksheet.questionIds.length} questions`);
  }

  /**
   * Convert MongoDB document to IExerciseQuestion interface
   */
  private convertMongoDocToIExerciseQuestion(doc: WorksheetQuestionDocument): IExerciseQuestion {
    return {
      id: doc.questionId, // Use the questionId field instead of MongoDB _id
      position: doc.position,
      order: doc.position, // For backward compatibility
      points: doc.points,
      type: doc.type,
      content: doc.content,
      options: doc.options,
      answer: doc.answer,
      explain: doc.explain,
      subject: doc.subject,
      parentSubject: doc.parentSubject,
      childSubject: doc.childSubject,
      topic: doc.topic,
      subtopic: doc.subtopic,
      grade: doc.grade,
      difficulty: doc.difficulty,
      media: doc.media,
      imagePrompt: doc.imagePrompt,
      imageUrl: doc.imageUrl,
      image: doc.image,
      status: doc.status as any, // Type conversion for status enum
      metadata: doc.metadata,
      audit: doc.audit,
      schoolId: doc.schoolId
    };
  }

  /**
   * Update cache by fetching fresh data from WorksheetPromptResult
   */
  private async updateQuestionCacheFromMongoDB(
    worksheetId: string,
    user: UserContext
  ): Promise<void> {
    try {
      // Ensure MongoDB counts are consistent before updating cache
      await this.ensureMongoDBCountsConsistent(worksheetId);

      // Fetch all questions for this worksheet using the new method
      const questions = await this.getWorksheetQuestions(worksheetId, user);

      // Update Redis cache
      await this.enhancedCacheService.cacheWorksheetQuestions(
        worksheetId,
        questions,
        user.schoolId || undefined
      );

      this.logger.debug(`Updated cache from WorksheetPromptResult for worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Failed to update question cache from WorksheetPromptResult for worksheet ${worksheetId}`, error);
      // Don't throw error - cache update failure shouldn't fail the operation
    }
  }

  /**
   * Get all questions for a worksheet from WorksheetPromptResult
   */
  async getWorksheetQuestions(
    worksheetId: string,
    user: UserContext
  ): Promise<IExerciseQuestion[]> {
    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Fetch questions from WorksheetPromptResult
    const promptResult = await this.worksheetPromptResultModel.findOne({
      worksheetId: worksheetId
    });

    if (!promptResult || !promptResult.promptResult?.result) {
      return [];
    }

    // Convert to IExerciseQuestion format, maintaining order from worksheet.questionIds
    const questions: IExerciseQuestion[] = [];

    if (worksheet.questionIds && worksheet.questionIds.length > 0) {
      // Create a map for quick lookup
      const questionMap = new Map();
      promptResult.promptResult.result.forEach((question: any) => {
        if (question.id) {
          questionMap.set(question.id, question);
        }
      });

      // Return questions in the order specified by worksheet.questionIds
      worksheet.questionIds.forEach((questionId, index) => {
        const questionData = questionMap.get(questionId);
        if (questionData) {
          questions.push(this.convertExerciseQuestionItemToIExerciseQuestion(questionData, index + 1));
        }
      });
    }

    return questions;
  }

  /**
   * Get a single question from WorksheetPromptResult
   */
  async getWorksheetQuestion(
    worksheetId: string,
    questionId: string,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Verify question belongs to this worksheet
    if (!worksheet.questionIds?.includes(questionId)) {
      throw new NotFoundException(`Question ${questionId} not found in worksheet ${worksheetId}`);
    }

    // Fetch question from WorksheetPromptResult
    const result = await this.findQuestionInPromptResult(worksheetId, questionId);
    if (!result) {
      throw new NotFoundException(`Question ${questionId} not found in database`);
    }

    // Get position from worksheet.questionIds array
    const position = worksheet.questionIds.indexOf(questionId) + 1;

    return this.convertExerciseQuestionItemToIExerciseQuestion(result.question, position);
  }

  /**
   * Check if a question exists in a worksheet
   */
  async questionExistsInWorksheet(
    worksheetId: string,
    questionId: string
  ): Promise<boolean> {
    const worksheet = await this.worksheetRepository.findOne({
      where: { id: worksheetId }
    });

    if (!worksheet || !worksheet.questionIds) {
      return false;
    }

    return worksheet.questionIds.includes(questionId);
  }

  /**
   * Create a new question from DTO
   */
  private async createQuestion(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet
  ): Promise<IExerciseQuestion> {
    const questionId = uuidv4();
    const currentQuestions = worksheet.questions || [];
    const position = questionDto.position || currentQuestions.length + 1;

    // If questionPoolId is provided, get the question from the pool
    if (questionDto.questionPoolId) {
      return this.createQuestionFromPool(questionDto, user, worksheet, questionId, position);
    }

    // Otherwise, create a new question from the provided data
    return this.createQuestionFromDto(questionDto, user, worksheet, questionId, position);
  }

  /**
   * Create a question from the question pool
   */
  private async createQuestionFromPool(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet,
    questionId: string,
    position: number
  ): Promise<IExerciseQuestion> {
    // Get the question from the pool
    const poolQuestion = await this.questionPoolService.getQuestionById(questionDto.questionPoolId!);

    if (!poolQuestion) {
      throw new NotFoundException(`Question with ID ${questionDto.questionPoolId} not found in question pool`);
    }

    // Validate user access to the pool question
    this.validatePoolQuestionAccess(poolQuestion, user);

    // Create the worksheet question based on the pool question
    const newQuestion: IExerciseQuestion = {
      id: questionId,
      position,
      points: questionDto.points || 1,

      // Copy question content from pool
      type: poolQuestion.type as any,
      content: poolQuestion.content,
      options: poolQuestion.options || [],
      answer: poolQuestion.answer || [],
      explain: poolQuestion.explain || '',

      // Copy subject and academic information
      subject: poolQuestion.subject,
      parentSubject: poolQuestion.parentSubject,
      childSubject: poolQuestion.childSubject,
      grade: poolQuestion.grade,
      difficulty: poolQuestion.difficultyLevel as any,

      // Copy media information
      image: poolQuestion.image,
      imagePrompt: poolQuestion.imagePrompt,

      // Override with any provided values from DTO
      ...(questionDto.optionTypeId && { optionTypeId: questionDto.optionTypeId }),
      ...(questionDto.optionValueId && { optionValueId: questionDto.optionValueId }),

      // Add pool reference for tracking
      questionPoolId: questionDto.questionPoolId,
      sourceType: 'question_pool',

      // Audit fields
      audit: {
        createdBy: user.sub,
        createdAt: new Date(),
        updatedBy: user.sub,
        updatedAt: new Date(),
        version: 1
      },

      // School association
      schoolId: user.schoolId || undefined
    };

    // Convert to ExerciseQuestionItem and add to WorksheetPromptResult
    const questionItem = this.convertIExerciseQuestionToExerciseQuestionItem(newQuestion);
    await this.addQuestionToPromptResult(worksheet.id, questionItem, position - 1);

    // Update pool question usage statistics
    await this.updatePoolQuestionUsage(questionDto.questionPoolId!);

    return newQuestion;
  }

  /**
   * Create a question from DTO data (original functionality)
   */
  private createQuestionFromDto(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet,
    questionId: string,
    position: number
  ): IExerciseQuestion {

    const newQuestion: IExerciseQuestion = {
      id: questionId,
      position,
      points: questionDto.points || 1,
      type: questionDto.type,
      content: questionDto.content,
      options: questionDto.options,
      answer: questionDto.answer,
      explain: questionDto.explain,
      subject: questionDto.subject,
      parentSubject: questionDto.parentSubject,
      childSubject: questionDto.childSubject,
      topic: questionDto.topic,
      subtopic: questionDto.subtopic,
      grade: questionDto.grade,
      difficulty: questionDto.difficulty,
      media: questionDto.media,
      imagePrompt: questionDto.imagePrompt,
      imageUrl: questionDto.imageUrl,
      status: questionDto.status, // Use the status from the base DTO
      isPublic: questionDto.isPublic,
      metadata: questionDto.metadata,
      // Database option references (if provided)
      optionTypeId: questionDto.optionTypeId,
      optionValueId: questionDto.optionValueId,
      // Audit fields
      audit: {
        createdBy: user.sub,
        createdAt: new Date(),
        updatedBy: user.sub,
        updatedAt: new Date(),
        version: 1
      },
      // School association
      schoolId: user.schoolId || undefined
    };

    return newQuestion;
  }

  /**
   * Update worksheet with new question
   */
  private async updateWorksheetWithNewQuestion(
    worksheet: Worksheet,
    newQuestion: IExerciseQuestion,
    user: UserContext
  ): Promise<void> {
    if (!worksheet.questions) {
      worksheet.questions = [];
    }

    worksheet.questions.push(newQuestion);
    worksheet.totalQuestions = worksheet.questions.length;
    worksheet.lastModifiedBy = user.sub;

    await this.worksheetRepository.save(worksheet);
  }

  /**
   * Update Redis cache with provided questions
   */
  @MonitorDbPerformance('updateCache', 'worksheet_questions')
  private async updateQuestionCache(
    worksheetId: string,
    questions: IExerciseQuestion[],
    user: UserContext
  ): Promise<void> {
    try {
      // Update Redis cache only (WorksheetPromptResult is the source of truth now)
      await this.enhancedCacheService.cacheWorksheetQuestions(
        worksheetId,
        questions,
        user.schoolId || undefined
      );

      this.logger.debug(`Updated Redis cache for worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Failed to update question cache for worksheet ${worksheetId}`, error);
      // Don't throw error - cache update failure shouldn't fail the operation
    }
  }

  /**
   * Remove a question from a worksheet
   */
  async removeQuestionFromWorksheet(
    worksheetId: string,
    questionId: string,
    user: UserContext,
    options: {
      replaceWithRandomQuestion?: boolean;
    } = { replaceWithRandomQuestion: true }
  ): Promise<void> {
    this.logger.log(`Removing question ${questionId} from worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);
    // Step 2: Find and validate the question exists in the worksheet
    const questionToRemove = await this.findQuestionInWorksheet(worksheet, questionId);

    // Step 3: Validate minimum questions requirement
    await this.validateMinimumQuestions(worksheet);

    // Step 4: Remove question from worksheet and reorder remaining questions
    await this.removeQuestionAndReorder(worksheet, questionId, user);

    // Step 5: Add replacement question if requested
    if (options.replaceWithRandomQuestion) {
      try {
        this.logger.log(`🔄 Starting replacement process for worksheet ${worksheetId}`);

        // Reload the worksheet to ensure we have the latest state after removal
        const updatedWorksheet = await this.validateWorksheetAccess(worksheetId, user);
        await this.findAndAddReplacementQuestion(updatedWorksheet, questionToRemove, user);

        // Verify the replacement was added to both databases
        const verifyDoc = await this.worksheetPromptResultModel.findOne({ worksheetId });
        const verifyWorksheet = await this.worksheetRepository.findOne({ where: { id: worksheetId } });

        this.logger.log(`📊 Post-replacement MongoDB count: ${verifyDoc?.promptResult?.result?.length || 0}`);
        this.logger.log(`📊 Post-replacement PostgreSQL count: ${verifyWorksheet?.questionIds?.length || 0}`);

        // Check for sync issues
        if (verifyDoc?.promptResult?.result?.length !== verifyWorksheet?.questionIds?.length) {
          this.logger.error(`❌ SYNC ISSUE DETECTED: MongoDB has ${verifyDoc?.promptResult?.result?.length || 0} questions, PostgreSQL has ${verifyWorksheet?.questionIds?.length || 0} questions`);
        }

      } catch (error) {
        this.logger.error(`❌ Failed to add replacement question for worksheet ${worksheetId}: ${error.message}`, error.stack);
        // Continue without replacement - don't fail the removal operation
      }
    }

    // Step 6: Update cache and invalidate Redis cache
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);
    await this.enhancedCacheService.invalidateWorksheetCache(worksheetId);

    // Step 7: Emit real-time update
    await this.emitQuestionUpdate(worksheetId, 'question_removed', {
      questionId,
      totalQuestions: worksheet.questionIds?.length || 0,
      worksheetId
    }, user.sub);

    // Step 8: Log audit event
    await this.auditService.logQuestionRemoved(worksheetId, questionId, questionToRemove, user);

    this.logger.log(`Successfully removed question ${questionId} from worksheet ${worksheetId}`);
  }

  /**
   * Find a question in the worksheet and return it (from WorksheetPromptResult)
   */
  private async findQuestionInWorksheet(
    worksheet: Worksheet,
    questionId: string
  ): Promise<IExerciseQuestion> {
    // Check if question exists in worksheet questionIds
    if (!worksheet.questionIds?.includes(questionId)) {
      throw new NotFoundException(
        `Question with ID ${questionId} not found in worksheet ${worksheet.id}`
      );
    }

    // Fetch from WorksheetPromptResult
    const result = await this.findQuestionInPromptResult(worksheet.id, questionId);
    if (!result) {
      throw new NotFoundException(
        `Question with ID ${questionId} not found in database`
      );
    }

    // Get position from worksheet.questionIds array
    const position = worksheet.questionIds.indexOf(questionId) + 1;
    return this.convertExerciseQuestionItemToIExerciseQuestion(result.question, position);
  }

  /**
   * Validate that removing a question won't violate minimum requirements
   */
  private async validateMinimumQuestions(worksheet: Worksheet): Promise<void> {
    const currentQuestionCount = worksheet.questionIds?.length || 0;
    const minQuestions = 1; // Minimum 1 question per worksheet

    if (currentQuestionCount <= minQuestions) {
      throw new BadRequestException(
        `Cannot remove question. Worksheet must have at least ${minQuestions} question(s). Current count: ${currentQuestionCount}`
      );
    }
  }

  /**
   * Remove question from worksheet and reorder remaining questions
   */
  private async removeQuestionAndReorder(
    worksheet: Worksheet,
    questionId: string,
    user: UserContext
  ): Promise<void> {
    if (!worksheet.questionIds || worksheet.questionIds.length === 0) {
      throw new NotFoundException('No questions found in worksheet');
    }

    // Find and remove the question ID from PostgreSQL
    const questionIndex = worksheet.questionIds.findIndex(id => id === questionId);

    if (questionIndex === -1) {
      throw new NotFoundException(`Question with ID ${questionId} not found in worksheet`);
    }

    // Remove from PostgreSQL questionIds array
    worksheet.questionIds.splice(questionIndex, 1);

    // Remove from WorksheetPromptResult
    await this.removeQuestionFromPromptResult(worksheet.id, questionId);

    // Note: Position reordering is handled automatically by the order of questionIds in PostgreSQL
    // The WorksheetPromptResult questions will be reordered when accessed via getWorksheetQuestions

    // Update worksheet metadata
    worksheet.totalQuestions = worksheet.questionIds.length;
    worksheet.lastModifiedBy = user.sub;

    // Save the updated worksheet
    await this.worksheetRepository.save(worksheet);
  }

  /**
   * Find and add a replacement question from the question pool
   */
  private async findAndAddReplacementQuestion(
    worksheet: Worksheet,
    removedQuestion: any,
    user: UserContext
  ): Promise<void> {
    this.logger.log(`Finding replacement question for worksheet ${worksheet.id}`);

    try {
      // Step 1: Extract criteria from the removed question
      const searchCriteria = this.extractQuestionCriteria(removedQuestion, worksheet);

      // Step 2: Get user's excluded question IDs
      const excludeQuestionIds = await this.userQuestionHistoryService.getQuestionIdsToExclude(
        user.sub,
        {
          excludeUserChosenQuestions: true,
          schoolId: user.schoolId || undefined
        }
      );

      // Step 3: Get a random replacement question from the pool
      // Use the legacy getRandomQuestions method which supports exclusions
      const allReplacementQuestions = await this.questionPoolService.getRandomQuestions(
        {
          subject: searchCriteria.subject,
          parentSubject: searchCriteria.parentSubject,
          childSubject: searchCriteria.childSubject,
          type: searchCriteria.type,
          grade: searchCriteria.grade,
          status: 'active'
        },
        5 // Get more than needed to have options for filtering
      );

      // Filter out questions that user has already chosen
      const filteredQuestions = allReplacementQuestions.filter(
        question => !excludeQuestionIds.includes((question as any)._id.toString())
      );

      const replacementQuestions = filteredQuestions.slice(0, 1);

      if (replacementQuestions.length === 0) {
        this.logger.warn(`No replacement question found for criteria: ${JSON.stringify(searchCriteria)}`);
        return;
      }

      const replacementQuestion = replacementQuestions[0] as any;
      this.logger.log(`Found replacement question: ${replacementQuestion._id}`);

      // Step 4: Add the replacement question to the worksheet
      const addQuestionDto: AddQuestionToWorksheetDto = {
        questionPoolId: replacementQuestion._id.toString(),
        type: replacementQuestion.type as any,
        content: replacementQuestion.content,
        options: replacementQuestion.options || [],
        answer: replacementQuestion.answer || [],
        explain: replacementQuestion.explain || '',
        subject: replacementQuestion.subject,
        parentSubject: replacementQuestion.parentSubject,
        childSubject: replacementQuestion.childSubject,
        grade: replacementQuestion.grade,
        difficulty: replacementQuestion.difficulty as any
      };

      this.logger.log(`📝 Calling addQuestionToWorksheet with questionPoolId: ${addQuestionDto.questionPoolId}`);
      await this.addQuestionToWorksheet(worksheet.id, addQuestionDto, user);
      this.logger.log(`✅ Successfully added replacement question to worksheet ${worksheet.id}`);

    } catch (error) {
      this.logger.error(`Error finding replacement question: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Extract search criteria from a removed question and worksheet context
   */
  private extractQuestionCriteria(removedQuestion: any, worksheet: Worksheet): any {
    // Start with criteria from the removed question
    const criteria: any = {
      subject: removedQuestion.subject,
      parentSubject: removedQuestion.parentSubject,
      childSubject: removedQuestion.childSubject,
      type: removedQuestion.type,
      grade: removedQuestion.grade,
      difficulty: removedQuestion.difficulty
    };

    // If removed question doesn't have complete criteria, try to get from worksheet options
    if (worksheet.selectedOptions && worksheet.selectedOptions.length > 0) {
      for (const option of worksheet.selectedOptions) {
        if (!criteria.subject && option.optionType?.key === 'subject') {
          criteria.subject = option.optionValue?.value || option.text;
        }
        if (!criteria.parentSubject && option.optionType?.key === 'parentSubject') {
          criteria.parentSubject = option.optionValue?.value || option.text;
        }
        if (!criteria.childSubject && option.optionType?.key === 'childSubject') {
          criteria.childSubject = option.optionValue?.value || option.text;
        }
        if (!criteria.grade && option.optionType?.key === 'grade') {
          criteria.grade = option.optionValue?.value || option.text;
        }
      }
    }

    // Remove undefined values to avoid issues with MongoDB queries
    Object.keys(criteria).forEach(key => {
      if (criteria[key] === undefined || criteria[key] === null) {
        delete criteria[key];
      }
    });

    this.logger.debug(`Extracted criteria for replacement question: ${JSON.stringify(criteria)}`);
    return criteria;
  }

  /**
   * Bulk add replacement questions for removed questions
   */
  private async bulkAddReplacementQuestions(
    worksheet: Worksheet,
    numberOfReplacements: number,
    user: UserContext
  ): Promise<void> {
    this.logger.log(`Finding ${numberOfReplacements} replacement questions for worksheet ${worksheet.id}`);

    try {
      // Step 1: Extract general criteria from worksheet options
      const searchCriteria = this.extractWorksheetCriteria(worksheet);

      // Step 2: Get user's excluded question IDs
      const excludeQuestionIds = await this.userQuestionHistoryService.getQuestionIdsToExclude(
        user.sub,
        {
          excludeUserChosenQuestions: true,
          schoolId: user.schoolId || undefined
        }
      );

      // Step 3: Get multiple random replacement questions from the pool
      // Use the legacy getRandomQuestions method which supports exclusions
      const allReplacementQuestions = await this.questionPoolService.getRandomQuestions(
        {
          subject: searchCriteria.subject,
          parentSubject: searchCriteria.parentSubject,
          childSubject: searchCriteria.childSubject,
          grade: searchCriteria.grade,
          status: 'active'
        },
        numberOfReplacements * 3 // Get more than needed to have options
      );

      // Filter out questions that user has already chosen
      const filteredQuestions = allReplacementQuestions.filter(
        question => !excludeQuestionIds.includes((question as any)._id.toString())
      );

      const replacementQuestions = filteredQuestions.slice(0, numberOfReplacements);

      if (replacementQuestions.length === 0) {
        this.logger.warn(`No replacement questions found for criteria: ${JSON.stringify(searchCriteria)}`);
        return;
      }

      this.logger.log(`Found ${replacementQuestions.length} replacement questions`);

      // Step 4: Add the replacement questions to the worksheet
      for (const replacementQuestion of replacementQuestions) {
        const addQuestionDto: AddQuestionToWorksheetDto = {
          questionPoolId: (replacementQuestion as any)._id.toString(),
          type: (replacementQuestion as any).type,
          content: (replacementQuestion as any).content,
          options: (replacementQuestion as any).options || [],
          answer: (replacementQuestion as any).answer || [],
          explain: (replacementQuestion as any).explain || '',
          subject: (replacementQuestion as any).subject,
          parentSubject: (replacementQuestion as any).parentSubject,
          childSubject: (replacementQuestion as any).childSubject,
          grade: (replacementQuestion as any).grade,
          difficulty: (replacementQuestion as any).difficulty
        };

        await this.addQuestionToWorksheet(worksheet.id, addQuestionDto, user);
      }

      this.logger.log(`Successfully added ${replacementQuestions.length} replacement questions to worksheet ${worksheet.id}`);

    } catch (error) {
      this.logger.error(`Error finding bulk replacement questions: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Extract search criteria from worksheet options
   */
  private extractWorksheetCriteria(worksheet: Worksheet): any {
    const criteria: any = {};

    // Extract criteria from worksheet selectedOptions
    if (worksheet.selectedOptions && worksheet.selectedOptions.length > 0) {
      for (const option of worksheet.selectedOptions) {
        if (option.optionType?.key === 'subject') {
          criteria.subject = option.optionValue?.value || option.text;
        }
        if (option.optionType?.key === 'parentSubject') {
          criteria.parentSubject = option.optionValue?.value || option.text;
        }
        if (option.optionType?.key === 'childSubject') {
          criteria.childSubject = option.optionValue?.value || option.text;
        }
        if (option.optionType?.key === 'grade') {
          criteria.grade = option.optionValue?.value || option.text;
        }
      }
    }

    // Remove undefined values to avoid issues with MongoDB queries
    Object.keys(criteria).forEach(key => {
      if (criteria[key] === undefined || criteria[key] === null) {
        delete criteria[key];
      }
    });

    this.logger.debug(`Extracted worksheet criteria for bulk replacement: ${JSON.stringify(criteria)}`);
    return criteria;
  }

  /**
   * Update a question in a worksheet (PATCH - partial update)
   */
  async updateQuestionInWorksheet(
    worksheetId: string,
    questionId: string,
    updateDto: UpdateWorksheetQuestionDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    this.logger.log(`Updating question ${questionId} in worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Check if user can edit this question (has lock)
    const canEdit = await this.lockingService.canEditQuestion(worksheetId, questionId, user.sub);
    if (!canEdit) {
      throw new ConflictException('Question is locked by another user. Acquire lock before editing.');
    }

    // Step 2: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 3: Get existing question from MongoDB
    const existingQuestion = await this.getWorksheetQuestion(worksheetId, questionId, user);

    // Step 4: Validate optimistic locking if version is provided
    if (updateDto.version !== undefined) {
      await this.validateQuestionVersion(existingQuestion, updateDto.version);
    }

    // Step 5: Validate the update data
    await this.validateQuestionUpdate(updateDto, questionId, worksheetId);

    // Step 6: Apply updates to WorksheetPromptResult
    const updateFields: any = {};
    if (updateDto.content !== undefined) updateFields.content = updateDto.content;
    if (updateDto.options !== undefined) updateFields.options = updateDto.options;
    if (updateDto.answer !== undefined) updateFields.answer = updateDto.answer;
    if (updateDto.explain !== undefined) updateFields.explain = updateDto.explain;
    if (updateDto.points !== undefined) updateFields.points = updateDto.points;
    if (updateDto.difficulty !== undefined) updateFields.difficulty = updateDto.difficulty;
    if (updateDto.media !== undefined) updateFields.media = updateDto.media;
    if (updateDto.type !== undefined) updateFields.type = updateDto.type;

    // Update audit information
    if (!updateFields.audit) updateFields.audit = existingQuestion.audit || {};
    updateFields.audit.updatedBy = user.sub;
    updateFields.audit.updatedAt = new Date();
    updateFields.audit.version = (existingQuestion.audit?.version || 1) + 1;

    // Add to change log
    if (!updateFields.audit.changeLog) updateFields.audit.changeLog = existingQuestion.audit?.changeLog || [];
    updateFields.audit.changeLog.push({
      timestamp: new Date(),
      userId: user.sub,
      action: 'partial_update',
      changes: updateFields,
      reason: updateDto.updateReason || 'Question updated'
    });

    const updatedQuestionItem = await this.updateQuestionInPromptResult(worksheetId, questionId, updateFields);

    if (!updatedQuestionItem) {
      throw new NotFoundException(`Question ${questionId} not found for update`);
    }

    // Get position from worksheet.questionIds array
    const position = worksheet.questionIds?.indexOf(questionId) + 1 || 1;
    const updatedQuestion = this.convertExerciseQuestionItemToIExerciseQuestion(updatedQuestionItem, position);

    // Step 7: Update worksheet metadata
    worksheet.lastModifiedBy = user.sub;

    // Step 8: Save the updated worksheet
    await this.worksheetRepository.save(worksheet);

    // Step 9: Update cache from MongoDB
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);

    // Step 10: Emit real-time updates (both legacy and collaboration)
    await this.emitQuestionUpdate(worksheetId, 'question_updated', {
      question: updatedQuestion,
      questionId,
      worksheetId,
      updateType: 'partial'
    }, user.sub);

    // Step 10b: Emit collaboration event
    await this.collaborationGateway.broadcastQuestionUpdate(
      worksheetId,
      CollaborationEvent.QUESTION_UPDATED_REALTIME,
      {
        question: updatedQuestion,
        questionId,
        updateType: 'partial',
        updatedBy: user.sub
      },
      user.sub
    );

    // Step 11: Log audit event
    await this.auditService.logQuestionUpdatedInWorksheet(worksheetId, questionId, user, updateDto);

    this.logger.log(`Successfully updated question ${questionId} in worksheet ${worksheetId}`);
    return updatedQuestion;
  }

  /**
   * Replace a question in a worksheet (PUT - full replacement)
   */
  async replaceQuestionInWorksheet(
    worksheetId: string,
    questionId: string,
    replaceDto: ReplaceWorksheetQuestionDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    this.logger.log(`Replacing question ${questionId} in worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 2: Get existing question from MongoDB
    const existingQuestion = await this.getWorksheetQuestion(worksheetId, questionId, user);

    // Step 3: Validate optimistic locking
    await this.validateQuestionVersion(existingQuestion, replaceDto.version);

    // Step 4: Validate the replacement data
    await this.validateQuestionReplacement(replaceDto);

    // Step 5: Replace the question in WorksheetPromptResult
    const replacementFields = {
      type: replaceDto.type,
      content: replaceDto.content,
      options: replaceDto.options,
      answer: replaceDto.answer,
      explain: replaceDto.explain,
      points: replaceDto.points || existingQuestion.points,
      difficulty: replaceDto.difficulty,
      media: replaceDto.media,
      subject: replaceDto.subject,
      parentSubject: replaceDto.parentSubject,
      childSubject: replaceDto.childSubject,
      topic: replaceDto.topic,
      subtopic: replaceDto.subtopic,
      grade: replaceDto.grade,
      imagePrompt: replaceDto.imagePrompt,
      imageUrl: replaceDto.imageUrl,
      metadata: replaceDto.metadata,
      audit: {
        ...existingQuestion.audit,
        updatedBy: user.sub,
        updatedAt: new Date(),
        version: (existingQuestion.audit?.version || 1) + 1,
        changeLog: [
          ...(existingQuestion.audit?.changeLog || []),
          {
            timestamp: new Date(),
            userId: user.sub,
            action: 'full_replacement',
            changes: replaceDto,
            reason: replaceDto.updateReason || 'Question replaced'
          }
        ]
      }
    };

    const updatedQuestionItem = await this.updateQuestionInPromptResult(worksheetId, questionId, replacementFields);

    if (!updatedQuestionItem) {
      throw new NotFoundException(`Question ${questionId} not found for replacement`);
    }

    // Get position from worksheet.questionIds array
    const position = worksheet.questionIds?.indexOf(questionId) + 1 || 1;
    const replacementQuestion = this.convertExerciseQuestionItemToIExerciseQuestion(updatedQuestionItem, position);

    // Step 6: Update worksheet metadata
    worksheet.lastModifiedBy = user.sub;

    // Step 7: Save the updated worksheet
    await this.worksheetRepository.save(worksheet);

    // Step 8: Update cache from MongoDB
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);

    // Step 9: Emit real-time update
    await this.emitQuestionUpdate(worksheetId, 'question_updated', {
      question: replacementQuestion,
      questionId,
      worksheetId,
      updateType: 'full'
    }, user.sub);

    // Step 10: Log audit event
    await this.auditService.logQuestionReplaced(worksheetId, questionId, user, replaceDto);

    this.logger.log(`Successfully replaced question ${questionId} in worksheet ${worksheetId}`);
    return replacementQuestion;
  }

  /**
   * Reorder questions in a worksheet (bulk operation)
   */
  async reorderQuestionsInWorksheet(
    worksheetId: string,
    reorderDto: BulkReorderQuestionsDto,
    user: UserContext
  ): Promise<{
    worksheetId: string;
    totalQuestions: number;
    reorderedQuestions: Array<{
      questionId: string;
      oldPosition: number;
      newPosition: number;
    }>;
    version: number;
  }> {
    this.logger.log(`Reordering ${reorderDto.reorders.length} questions in worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 2: Validate optimistic locking
    await this.validateWorksheetVersion(worksheet, user);

    // Step 3: Validate reorder operations
    await this.validateReorderOperations(worksheet, reorderDto.reorders);

    // Step 4: Perform the reordering
    const reorderResults = await this.performQuestionReordering(worksheet, reorderDto.reorders, user);

    // Step 5: Save the updated worksheet
    await this.worksheetRepository.save(worksheet);

    // Step 6: Update cache from MongoDB
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);

    // Step 7: Emit real-time updates
    await this.emitQuestionUpdate(worksheetId, 'questions_reordered', {
      worksheetId,
      totalQuestions: worksheet.questionIds?.length || 0,
      reorderedQuestions: reorderResults,
      version: worksheet.questionMetadata?.questionVersion || 1
    }, user.sub);

    // Step 8: Log audit events
    for (const result of reorderResults) {
      await this.auditService.logQuestionMoved(
        result.questionId,
        result.oldPosition,
        result.newPosition,
        worksheetId,
        worksheetId,
        user.sub,
        { reorderOperation: true }
      );
    }

    this.logger.log(`Successfully reordered ${reorderResults.length} questions in worksheet ${worksheetId}`);

    return {
      worksheetId,
      totalQuestions: worksheet.questionIds?.length || 0,
      reorderedQuestions: reorderResults,
      version: worksheet.questionMetadata?.questionVersion || 1
    };
  }

  /**
   * Find question index in worksheet questionIds array
   */
  private async findQuestionIndex(worksheet: Worksheet, questionId: string): Promise<number> {
    if (!worksheet.questionIds || worksheet.questionIds.length === 0) {
      throw new NotFoundException('No questions found in worksheet');
    }

    const questionIndex = worksheet.questionIds.findIndex(id => id === questionId);
    if (questionIndex === -1) {
      throw new NotFoundException(`Question with ID ${questionId} not found in worksheet`);
    }

    return questionIndex;
  }

  /**
   * Validate question version for optimistic locking
   */
  private async validateQuestionVersion(
    existingQuestion: IExerciseQuestion,
    providedVersion: number
  ): Promise<void> {
    const currentVersion = existingQuestion.audit?.version || 1;

    if (currentVersion !== providedVersion) {
      throw new ConflictException(
        `Question has been modified by another user. Expected version: ${providedVersion}, Current version: ${currentVersion}. Please refresh and try again.`
      );
    }
  }

  /**
   * Validate question update data
   */
  private async validateQuestionUpdate(
    updateDto: UpdateWorksheetQuestionDto,
    questionId: string,
    worksheetId: string
  ): Promise<void> {
    // Validate answer format based on question type
    if (updateDto.type && updateDto.answer) {
      // If new type is provided, validate against the new type
      await this.validateAnswerFormat(updateDto.type, updateDto.answer);
    } else if (updateDto.answer) {
      // If only answer is provided, fetch current type from WorksheetPromptResult
      await this.validateAnswerFormatFromDB(questionId, updateDto.answer, worksheetId);
    }

    // For options validation, we need to get the type
    if (updateDto.options) {
      let questionType = updateDto.type;
      if (!questionType) {
        // Fetch type from WorksheetPromptResult
        const result = await this.findQuestionInPromptResult(worksheetId, questionId);
        questionType = result?.question?.type as any;
      }
      if (questionType) {
        await this.validateQuestionOptions(updateDto.options, questionType);
      }
    }

    // Validate image prompt if provided
    if (updateDto.imagePrompt) {
      await this.validateImagePrompt(updateDto.imagePrompt);
    }

    // Validate educational standards compliance
    await this.validateEducationalStandards(updateDto);
  }

  /**
   * Validate question replacement data
   */
  private async validateQuestionReplacement(replaceDto: ReplaceWorksheetQuestionDto): Promise<void> {
    // Validate answer format
    await this.validateAnswerFormat(replaceDto.type, replaceDto.answer);

    // Validate options
    await this.validateQuestionOptions(replaceDto.options, replaceDto.type);

    // Validate image prompt if provided
    if (replaceDto.imagePrompt) {
      await this.validateImagePrompt(replaceDto.imagePrompt);
    }

    // Validate educational standards compliance
    await this.validateEducationalStandards(replaceDto);
  }

  /**
   * Apply partial updates to existing question
   */
  private async applyQuestionUpdate(
    existingQuestion: IExerciseQuestion,
    updateDto: UpdateWorksheetQuestionDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    const updatedQuestion: IExerciseQuestion = {
      ...existingQuestion,
      ...updateDto,
      id: existingQuestion.id, // Preserve ID
      audit: {
        createdAt: existingQuestion.audit?.createdAt || new Date(),
        updatedAt: new Date(),
        createdBy: existingQuestion.audit?.createdBy || user.sub,
        updatedBy: user.sub,
        version: (existingQuestion.audit?.version || 1) + 1,
        changeLog: [
          ...(existingQuestion.audit?.changeLog || []),
          {
            timestamp: new Date(),
            userId: user.sub,
            action: 'partial_update',
            changes: updateDto,
            reason: updateDto.updateReason
          }
        ]
      },
      schoolId: user.schoolId || undefined
    };

    return updatedQuestion;
  }

  /**
   * Create replacement question
   */
  private async createReplacementQuestion(
    existingQuestion: IExerciseQuestion,
    replaceDto: ReplaceWorksheetQuestionDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    const replacementQuestion: IExerciseQuestion = {
      ...replaceDto,
      id: existingQuestion.id, // Preserve ID
      order: existingQuestion.order, // Preserve order
      audit: {
        createdAt: existingQuestion.audit?.createdAt || new Date(),
        updatedAt: new Date(),
        createdBy: existingQuestion.audit?.createdBy || user.sub,
        updatedBy: user.sub,
        version: (existingQuestion.audit?.version || 1) + 1,
        changeLog: [
          ...(existingQuestion.audit?.changeLog || []),
          {
            timestamp: new Date(),
            userId: user.sub,
            action: 'full_replacement',
            changes: replaceDto,
            reason: replaceDto.updateReason
          }
        ]
      },
      schoolId: user.schoolId || undefined
    };

    return replacementQuestion;
  }

  /**
   * Validate answer format based on question type (with type provided)
   */
  private async validateAnswerFormat(type: string, answers: string[]): Promise<void> {
    switch (type) {
      case 'multiple_choice':
        if (answers.length === 0) {
          throw new BadRequestException('Multiple choice questions must have at least one correct answer');
        }
        break;
      case 'true_false':
        if (answers.length !== 1 || !['true', 'false'].includes(answers[0].toLowerCase())) {
          throw new BadRequestException('True/false questions must have exactly one answer: "true" or "false"');
        }
        break;
      case 'short_answer':
      case 'long_answer':
        if (answers.length === 0) {
          throw new BadRequestException('Answer questions must have at least one acceptable answer');
        }
        break;
      default:
        if (answers.length === 0) {
          throw new BadRequestException('Questions must have at least one correct answer');
        }
    }
  }

  /**
   * Validate answer format by fetching question type from WorksheetPromptResult
   */
  private async validateAnswerFormatFromDB(questionId: string, answers: string[], worksheetId: string): Promise<void> {
    // Fetch the question from WorksheetPromptResult to get the current type
    const result = await this.findQuestionInPromptResult(worksheetId, questionId);
    if (!result) {
      throw new NotFoundException(`Question with ID ${questionId} not found in database`);
    }

    // Use the type from WorksheetPromptResult
    await this.validateAnswerFormat(result.question.type, answers);
  }

  /**
   * Validate question options
   */
  private async validateQuestionOptions(options: string[], type: string): Promise<void> {
    if (type === 'multiple_choice' && options.length < 2) {
      throw new BadRequestException('Multiple choice questions must have at least 2 options');
    }

    if (type === 'true_false' && options.length !== 2) {
      throw new BadRequestException('True/false questions must have exactly 2 options');
    }

    // Check for duplicate options
    const uniqueOptions = new Set(options.map(opt => opt.trim().toLowerCase()));
    if (uniqueOptions.size !== options.length) {
      throw new BadRequestException('Question options must be unique');
    }
  }

  /**
   * Validate image prompt
   */
  private async validateImagePrompt(imagePrompt: string): Promise<void> {
    // Check for measurement requirements
    const measurementKeywords = ['cm', 'mm', 'inch', 'meter', 'feet', 'dimension', 'size'];
    const hasSpecificMeasurements = measurementKeywords.some(keyword =>
      imagePrompt.toLowerCase().includes(keyword)
    );

    if (hasSpecificMeasurements) {
      // Validate that specific dimensions are provided
      const dimensionPattern = /\d+\s*(cm|mm|inch|meter|feet|px)/i;
      if (!dimensionPattern.test(imagePrompt)) {
        throw new BadRequestException(
          'Image prompts with measurement requirements must include specific dimensions (e.g., "10cm", "5 inches")'
        );
      }
    }

    // Check prompt length
    if (imagePrompt.length > 500) {
      throw new BadRequestException('Image prompt must be 500 characters or less');
    }
  }

  /**
   * Validate educational standards compliance
   */
  private async validateEducationalStandards(questionData: any): Promise<void> {
    // Validate grade level appropriateness
    if (questionData.grade) {
      const validGrades = ['K', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];
      if (!validGrades.includes(questionData.grade)) {
        throw new BadRequestException(`Invalid grade level: ${questionData.grade}`);
      }
    }

    // Validate subject alignment
    if (questionData.subject && questionData.childSubject) {
      // Ensure child subject is related to parent subject
      await this.validateSubjectHierarchy(questionData.subject, questionData.childSubject);
    }

    // Validate content appropriateness
    if (questionData.content) {
      await this.validateContentAppropriatenesss(questionData.content, questionData.grade);
    }
  }

  /**
   * Validate subject hierarchy
   */
  private async validateSubjectHierarchy(parentSubject: string, childSubject: string): Promise<void> {
    // This would typically check against a database of valid subject hierarchies
    // For now, we'll do basic validation
    const subjectMappings = {
      'Mathematics': ['Algebra', 'Geometry', 'Calculus', 'Statistics', 'Arithmetic'],
      'Science': ['Physics', 'Chemistry', 'Biology', 'Earth Science'],
      'English': ['Literature', 'Grammar', 'Writing', 'Reading Comprehension'],
      'Social Studies': ['History', 'Geography', 'Civics', 'Economics']
    };

    if (subjectMappings[parentSubject] && !subjectMappings[parentSubject].includes(childSubject)) {
      this.logger.warn(`Potential subject hierarchy mismatch: ${parentSubject} -> ${childSubject}`);
    }
  }

  /**
   * Validate content appropriateness for grade level
   */
  private async validateContentAppropriatenesss(content: string, grade?: string): Promise<void> {
    if (!grade) return;

    // Basic content complexity validation based on grade
    const gradeNum = parseInt(grade) || 0;
    const wordCount = content.split(' ').length;
    const avgWordLength = content.split(' ').reduce((sum, word) => sum + word.length, 0) / wordCount;

    // Elementary grades (K-5) should have simpler content
    if (gradeNum <= 5) {
      if (wordCount > 50) {
        this.logger.warn(`Question content may be too long for grade ${grade}: ${wordCount} words`);
      }
      if (avgWordLength > 6) {
        this.logger.warn(`Question content may use complex words for grade ${grade}: avg ${avgWordLength} chars per word`);
      }
    }
  }

  /**
   * Validate worksheet version for optimistic locking
   */
  private async validateWorksheetVersion(worksheet: Worksheet, user: UserContext): Promise<void> {
    // Check if worksheet is locked by another user
    if (worksheet.questionMetadata?.lockStatus?.isLocked) {
      const lockedBy = worksheet.questionMetadata.lockStatus.lockedBy;
      if (lockedBy && lockedBy !== user.sub) {
        throw new ConflictException(
          `Worksheet is currently being edited by another user. Please try again later.`
        );
      }
    }
  }

  /**
   * Validate reorder operations
   */
  private async validateReorderOperations(
    worksheet: Worksheet,
    reorders: ReorderQuestionDto[]
  ): Promise<void> {
    console.log('validateReorderOperations', worksheet, reorders);
    if (!worksheet.questionIds || worksheet.questionIds.length === 0) {
      throw new BadRequestException('Worksheet has no questions to reorder');
    }

    const totalQuestions = worksheet.questionIds.length;
    const questionIds = worksheet.questionIds
    const newPositions = new Set<number>();

    // Validate each reorder operation
    for (const reorder of reorders) {
      // Check if question exists in worksheet
      if (!questionIds.includes(reorder.questionId)) {
        console.log('Question not found in worksheet', reorder.questionId, questionIds);
        throw new NotFoundException(`Question ${reorder.questionId} not found in worksheet`);
      }

      // Check if new position is valid
      if (reorder.newPosition < 1 || reorder.newPosition > totalQuestions) {
        throw new BadRequestException(
          `Invalid position ${reorder.newPosition}. Must be between 1 and ${totalQuestions}`
        );
      }

      // Check for duplicate positions
      if (newPositions.has(reorder.newPosition)) {
        throw new BadRequestException(
          `Duplicate position ${reorder.newPosition} found in reorder operations`
        );
      }
      newPositions.add(reorder.newPosition);
    }

    // The reordering logic will automatically handle position shifting,
    // so we don't need to validate position gaps here
  }

  /**
   * Perform the actual question reordering
   */
  private async performQuestionReordering(
    worksheet: Worksheet,
    reorders: ReorderQuestionDto[],
    user: UserContext
  ): Promise<Array<{
    questionId: string;
    oldPosition: number;
    newPosition: number;
  }>> {
    const results: Array<{
      questionId: string;
      oldPosition: number;
      newPosition: number;
    }> = [];

    if (!worksheet.questionIds || worksheet.questionIds.length === 0) {
      throw new BadRequestException('No questions found in worksheet');
    }

    // Track old positions before reordering
    const oldPositions = new Map<string, number>();
    worksheet.questionIds.forEach((questionId, index) => {
      oldPositions.set(questionId, index + 1);
    });

    // Process each reorder operation
    for (const reorder of reorders) {
      const questionId = reorder.questionId;
      const currentIndex = worksheet.questionIds.findIndex(id => id === questionId);

      if (currentIndex === -1) {
        this.logger.warn(`Question ${questionId} not found in worksheet ${worksheet.id}`);
        continue;
      }

      const oldPosition = currentIndex + 1;
      const newPosition = reorder.newPosition;

      if (oldPosition === newPosition) continue; // No change needed

      // Reorder the questionIds array in PostgreSQL
      const [movedQuestionId] = worksheet.questionIds.splice(currentIndex, 1);
      worksheet.questionIds.splice(newPosition - 1, 0, movedQuestionId);

      results.push({
        questionId,
        oldPosition,
        newPosition
      });
    }

    // Update MongoDB worksheetpromptresults to match the new PostgreSQL order
    await this.syncMongoDBQuestionOrder(worksheet, user);

    // Note: worksheet_questions collection is not used in current architecture
    // If it were used, we would update positions here, but since it's empty,
    // we skip this step to avoid unnecessary operations

    // Update worksheet metadata
    worksheet.lastModifiedBy = user.sub;
    worksheet.questionMetadata = {
      ...worksheet.questionMetadata,
      lastQuestionUpdate: new Date(),
      questionVersion: (worksheet.questionMetadata?.questionVersion || 1) + 1,
      hasUnsavedChanges: false
    };

    return results;
  }

  /**
   * Sync MongoDB worksheetpromptresults question order to match PostgreSQL
   */
  private async syncMongoDBQuestionOrder(worksheet: Worksheet, user: UserContext): Promise<void> {
    try {
      // Get the current worksheetpromptresults document
      const promptResult = await this.worksheetPromptResultModel.findOne({
        worksheetId: worksheet.id
      });

      if (!promptResult || !promptResult.promptResult?.result) {
        this.logger.warn(`No prompt result found for worksheet ${worksheet.id}, skipping MongoDB sync`);
        return;
      }

      // Create a map of questionId to question data for quick lookup
      const questionMap = new Map();
      promptResult.promptResult.result.forEach((question: any) => {
        if (question.id) {
          questionMap.set(question.id, question);
        }
      });

      // Reorder the questions array to match PostgreSQL questionIds order
      const reorderedQuestions = worksheet.questionIds
        .map(questionId => questionMap.get(questionId))
        .filter(question => question !== undefined); // Remove any missing questions

      // Update the MongoDB document with the new order and counts
      await this.worksheetPromptResultModel.updateOne(
        { worksheetId: worksheet.id },
        {
          $set: {
            'promptResult.result': reorderedQuestions,
            currentQuestionCount: reorderedQuestions.length,
            totalQuestionCount: reorderedQuestions.length,
            updatedAt: new Date()
          }
        }
      );

      this.logger.log(`Successfully synced MongoDB question order for worksheet ${worksheet.id}`);

      // Ensure counts are consistent after reordering
      await this.ensureMongoDBCountsConsistent(worksheet.id);
    } catch (error) {
      this.logger.error(`Failed to sync MongoDB question order for worksheet ${worksheet.id}:`, error);
      // Don't throw error to avoid breaking the reordering operation
    }
  }

  /**
   * Bulk add questions to a worksheet (synchronous operation)
   */
  async bulkAddQuestions(
    worksheetId: string,
    questions: AddQuestionToWorksheetDto[],
    user: UserContext,
    options: {
      insertPosition?: number;
      validateQuestions?: boolean;
      reason?: string;
    } = {}
  ): Promise<{
    success: boolean;
    successCount: number;
    failureCount: number;
    totalCount: number;
    successes: IExerciseQuestion[];
    failures: Array<{ item: AddQuestionToWorksheetDto; error: string; index: number }>;
    processingTimeMs: number;
  }> {
    const startTime = Date.now();
    this.logger.log(`Bulk adding ${questions.length} questions to worksheet ${worksheetId} by user ${user.sub}`);

    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Check if bulk addition would exceed question limit
    const currentCount = worksheet.questions?.length || 0;
    const maxQuestions = worksheet.maxQuestions || 100;
    if (currentCount + questions.length > maxQuestions) {
      throw new BadRequestException(
        `Bulk addition would exceed question limit. Current: ${currentCount}, Adding: ${questions.length}, Maximum: ${maxQuestions}`
      );
    }

    const successes: IExerciseQuestion[] = [];
    const failures: Array<{ item: AddQuestionToWorksheetDto; error: string; index: number }> = [];

    // Process each question
    for (let i = 0; i < questions.length; i++) {
      try {
        const questionDto = questions[i];
        const newQuestion = await this.createQuestion(questionDto, user, worksheet);

        // Add to worksheet
        if (!worksheet.questions) {
          worksheet.questions = [];
        }

        // Insert at specified position or at the end
        if (options.insertPosition && i === 0) {
          worksheet.questions.splice(options.insertPosition - 1, 0, newQuestion);
        } else {
          worksheet.questions.push(newQuestion);
        }

        successes.push(newQuestion);
      } catch (error) {
        failures.push({
          item: questions[i],
          error: error.message,
          index: i
        });
      }
    }

    // Update worksheet metadata
    worksheet.totalQuestions = worksheet.questions.length;
    worksheet.lastModifiedBy = user.sub;

    // Save worksheet
    await this.worksheetRepository.save(worksheet);

    // Update cache
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);

    // Emit real-time updates
    await this.emitQuestionUpdate(worksheetId, 'questions_bulk_added', {
      addedQuestions: successes,
      totalQuestions: worksheet.questionIds?.length || 0,
      worksheetId,
      reason: options.reason
    }, user.sub);

    // Log audit event
    await this.auditService.logBulkQuestionOperation(
      worksheetId,
      'bulk_add',
      user,
      { successCount: successes.length, failureCount: failures.length, reason: options.reason }
    );

    const processingTimeMs = Date.now() - startTime;
    this.logger.log(`Bulk add completed: ${successes.length} successes, ${failures.length} failures in ${processingTimeMs}ms`);

    return {
      success: failures.length === 0,
      successCount: successes.length,
      failureCount: failures.length,
      totalCount: questions.length,
      successes,
      failures,
      processingTimeMs
    };
  }

  /**
   * Bulk remove questions from a worksheet (synchronous operation)
   */
  async bulkRemoveQuestions(
    worksheetId: string,
    questionIds: string[],
    user: UserContext,
    options: {
      forceRemoval?: boolean;
      reason?: string;
      replaceWithRandomQuestions?: boolean;
    } = {}
  ): Promise<{
    success: boolean;
    successCount: number;
    failureCount: number;
    totalCount: number;
    successes: string[];
    failures: Array<{ questionId: string; error: string; index: number }>;
    processingTimeMs: number;
  }> {
    const startTime = Date.now();
    this.logger.log(`Bulk removing ${questionIds.length} questions from worksheet ${worksheetId} by user ${user.sub}`);

    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Check minimum questions requirement unless forced
    if (!options.forceRemoval) {
      const currentCount = worksheet.questionIds?.length || 0;
      const remainingCount = currentCount - questionIds.length;
      if (remainingCount < 1) {
        throw new BadRequestException(
          `Cannot remove ${questionIds.length} questions. Worksheet must have at least 1 question. Current count: ${currentCount}`
        );
      }
    }

    const successes: string[] = [];
    const failures: Array<{ questionId: string; error: string; index: number }> = [];

    // Process each question ID
    for (let i = 0; i < questionIds.length; i++) {
      try {
        const questionId = questionIds[i];

        // Find question in worksheet questionIds array
        const questionIndex = worksheet.questionIds?.findIndex(id => id === questionId);
        if (questionIndex === undefined || questionIndex === -1) {
          throw new Error(`Question with ID ${questionId} not found in worksheet`);
        }

        // Remove the question ID from PostgreSQL
        worksheet.questionIds.splice(questionIndex, 1);
        successes.push(questionId);
      } catch (error) {
        failures.push({
          questionId: questionIds[i],
          error: error.message,
          index: i
        });
      }
    }

    // Remove questions from WorksheetPromptResult (bulk operation for efficiency)
    if (successes.length > 0) {
      await this.bulkRemoveQuestionsFromPromptResult(worksheetId, successes);
    }

    // Add replacement questions if requested
    if (options.replaceWithRandomQuestions && successes.length > 0) {
      try {
        await this.bulkAddReplacementQuestions(worksheet, successes.length, user);
      } catch (error) {
        this.logger.warn(`Failed to add replacement questions for bulk removal in worksheet ${worksheetId}: ${error.message}`);
        // Continue without replacement - don't fail the removal operation
      }
    }

    // Note: Position reordering is handled automatically by the order of questionIds in PostgreSQL
    // The WorksheetPromptResult questions will be reordered when accessed via getWorksheetQuestions

    // Update worksheet metadata
    worksheet.totalQuestions = worksheet.questionIds?.length || 0;
    worksheet.lastModifiedBy = user.sub;

    // Save worksheet
    await this.worksheetRepository.save(worksheet);

    // Update cache
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);

    // Emit real-time updates
    await this.emitQuestionUpdate(worksheetId, 'questions_bulk_removed', {
      removedQuestionIds: successes,
      totalQuestions: worksheet.questionIds?.length || 0,
      worksheetId,
      reason: options.reason
    }, user.sub);

    // Log audit event
    await this.auditService.logBulkQuestionOperation(
      worksheetId,
      'bulk_remove',
      user,
      { successCount: successes.length, failureCount: failures.length, reason: options.reason }
    );

    const processingTimeMs = Date.now() - startTime;
    this.logger.log(`Bulk remove completed: ${successes.length} successes, ${failures.length} failures in ${processingTimeMs}ms`);

    return {
      success: failures.length === 0,
      successCount: successes.length,
      failureCount: failures.length,
      totalCount: questionIds.length,
      successes,
      failures,
      processingTimeMs
    };
  }

  /**
   * Bulk update questions in a worksheet (synchronous operation)
   */
  async bulkUpdateQuestions(
    worksheetId: string,
    updates: Array<{ questionId: string; updates: UpdateWorksheetQuestionDto }>,
    user: UserContext,
    options: {
      validateQuestions?: boolean;
      reason?: string;
    } = {}
  ): Promise<{
    success: boolean;
    successCount: number;
    failureCount: number;
    totalCount: number;
    successes: IExerciseQuestion[];
    failures: Array<{ item: { questionId: string; updates: UpdateWorksheetQuestionDto }; error: string; index: number }>;
    processingTimeMs: number;
  }> {
    const startTime = Date.now();
    this.logger.log(`Bulk updating ${updates.length} questions in worksheet ${worksheetId} by user ${user.sub}`);

    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    const successes: IExerciseQuestion[] = [];
    const failures: Array<{ item: { questionId: string; updates: UpdateWorksheetQuestionDto }; error: string; index: number }> = [];

    // Process each update
    for (let i = 0; i < updates.length; i++) {
      try {
        const { questionId, updates: updateDto } = updates[i];

        // Verify question exists in worksheet questionIds
        if (!worksheet.questionIds?.includes(questionId)) {
          throw new Error(`Question with ID ${questionId} not found in worksheet`);
        }

        // Get the question from WorksheetPromptResult
        const existingQuestion = await this.getWorksheetQuestion(worksheetId, questionId, user);
        if (!existingQuestion) {
          throw new Error(`Question with ID ${questionId} not found in WorksheetPromptResult`);
        }

        // Validate optimistic locking if version is provided
        if (updateDto.version !== undefined) {
          if (existingQuestion.audit?.version !== updateDto.version) {
            throw new Error(`Version mismatch. Expected ${updateDto.version}, got ${existingQuestion.audit?.version}`);
          }
        }

        // Apply the update to WorksheetPromptResult
        const updateFields: any = {};
        if (updateDto.content !== undefined) updateFields.content = updateDto.content;
        if (updateDto.options !== undefined) updateFields.options = updateDto.options;
        if (updateDto.answer !== undefined) updateFields.answer = updateDto.answer;
        if (updateDto.explain !== undefined) updateFields.explain = updateDto.explain;
        if (updateDto.points !== undefined) updateFields.points = updateDto.points;
        if (updateDto.difficulty !== undefined) updateFields.difficulty = updateDto.difficulty;
        if (updateDto.media !== undefined) updateFields.media = updateDto.media;

        // Update audit information
        if (!updateFields.audit) updateFields.audit = existingQuestion.audit || {};
        updateFields.audit.updatedBy = user.sub;
        updateFields.audit.updatedAt = new Date();
        updateFields.audit.version = (existingQuestion.audit?.version || 1) + 1;

        // Add to change log
        if (!updateFields.audit.changeLog) updateFields.audit.changeLog = existingQuestion.audit?.changeLog || [];
        updateFields.audit.changeLog.push({
          timestamp: new Date(),
          userId: user.sub,
          action: 'bulk_update',
          changes: updateFields,
          reason: options.reason || 'Bulk update operation'
        });

        const updatedQuestionItem = await this.updateQuestionInPromptResult(worksheetId, questionId, updateFields);

        if (updatedQuestionItem) {
          // Get position from worksheet.questionIds array
          const position = worksheet.questionIds?.indexOf(questionId) + 1 || 1;
          const updatedQuestion = this.convertExerciseQuestionItemToIExerciseQuestion(updatedQuestionItem, position);
          successes.push(updatedQuestion);
        }
      } catch (error) {
        failures.push({
          item: updates[i],
          error: error.message,
          index: i
        });
      }
    }

    // Update worksheet metadata
    worksheet.lastModifiedBy = user.sub;

    // Save worksheet
    await this.worksheetRepository.save(worksheet);

    // Update cache
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);

    // Emit real-time updates
    await this.emitQuestionUpdate(worksheetId, 'questions_bulk_updated', {
      updatedQuestions: successes,
      totalQuestions: worksheet.questionIds?.length || 0,
      worksheetId,
      reason: options.reason
    }, user.sub);

    // Log audit event
    await this.auditService.logBulkQuestionOperation(
      worksheetId,
      'bulk_update',
      user,
      { successCount: successes.length, failureCount: failures.length, reason: options.reason }
    );

    const processingTimeMs = Date.now() - startTime;
    this.logger.log(`Bulk update completed: ${successes.length} successes, ${failures.length} failures in ${processingTimeMs}ms`);

    return {
      success: failures.length === 0,
      successCount: successes.length,
      failureCount: failures.length,
      totalCount: updates.length,
      successes,
      failures,
      processingTimeMs
    };
  }

  /**
   * Emit real-time update via WebSocket
   */
  private async emitQuestionUpdate(
    worksheetId: string,
    event: string,
    data: any,
    excludeUserId?: string
  ): Promise<void> {
    try {
      // Emit to all users subscribed to this worksheet except the one who made the change
      this.socketGateway.server.to(`worksheet-${worksheetId}`).emit(event, {
        ...data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      this.logger.error(`Failed to emit ${event} for worksheet ${worksheetId}`, error);
      // Don't throw error - WebSocket failure shouldn't fail the operation
    }
  }

  /**
   * Validate user access to a pool question
   */
  private validatePoolQuestionAccess(poolQuestion: QuestionPool, user: UserContext): void {
    // Admin can access all questions
    if (user.role === EUserRole.ADMIN) {
      return;
    }

    // For now, all active questions in the pool are accessible to authenticated users
    // In the future, you could implement more granular access control based on:
    // - School-specific question pools
    // - Public/private question visibility
    // - User role-based restrictions

    if (poolQuestion.status !== 'active') {
      throw new BadRequestException('Question is not available for use');
    }
  }

  /**
   * Update pool question usage statistics
   */
  private async updatePoolQuestionUsage(questionPoolId: string): Promise<void> {
    try {
      // This could be implemented to track usage statistics
      // For now, we'll just log the usage
      this.logger.debug(`Question ${questionPoolId} used from pool`);

      // In the future, you could:
      // - Increment usage count in the pool question
      // - Track last used timestamp
      // - Update popularity metrics
      // await this.questionPoolService.incrementUsageCount(questionPoolId);
    } catch (error) {
      this.logger.error(`Failed to update pool question usage for ${questionPoolId}`, error);
      // Don't throw error - usage tracking failure shouldn't fail the operation
    }
  }
}
